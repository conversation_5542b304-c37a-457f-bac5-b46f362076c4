<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日任务追踪</title>
    <style>
        :root {
            --primary: #b980e0;
            --primary-light: #ead5f8;
            --high-priority: #f47c7c;
            --medium-priority: #f7a76a;
            --low-priority: #f7d977;
            --completed: #92ca92;
            --background: #fef5fa;
            --card: #ffffff;
            --text: #655673;
            --text-light: #a797b9;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--background);
            color: var(--text);
            background-image: linear-gradient(120deg, #fdf4f9 0%, #f5edfa 100%);
            min-height: 100vh;
        }

        .header {
            text-align: center;
            margin-bottom: 10px;
        }

        .header h1 {
            color: var(--primary);
            margin-bottom: 10px;
            font-weight: 600;
            font-size: 2.2rem;
            letter-spacing: 1px;
            text-shadow: 1px 1px 2px rgba(185, 128, 224, 0.1);
        }

        .date {
            font-size: 1.1rem;
            color: var(--text-light);
            font-weight: 300;
            margin-bottom: 25px;
        }

        .add-task-form {
            max-width: 700px;
            margin: 0 auto 30px auto;
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(185, 128, 224, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.8);
        }

        .form-title {
            font-size: 1.3rem;
            color: var(--primary);
            margin-bottom: 15px;
            text-align: center;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 0.9rem;
            color: var(--text);
        }

        .form-input {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #e8d8f3;
            border-radius: 10px;
            font-size: 1rem;
            color: var(--text);
            background-color: #fcf8ff;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        .form-input:focus {
            border-color: var(--primary);
            outline: none;
            box-shadow: 0 0 0 3px rgba(185, 128, 224, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #e8d8f3;
            border-radius: 10px;
            font-size: 1rem;
            color: var(--text);
            background-color: #fcf8ff;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23b980e0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 15px center;
            background-size: 16px;
        }

        .form-select:focus {
            border-color: var(--primary);
            outline: none;
            box-shadow: 0 0 0 3px rgba(185, 128, 224, 0.1);
        }

        .form-button {
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            display: block;
            margin: 0 auto;
            font-weight: 500;
        }

        .form-button:hover {
            background-color: #a66cd5;
            transform: translateY(-2px);
        }

        .form-button:active {
            transform: translateY(0);
        }

        .add-task-form {
            max-width: 700px;
            margin: 0 auto 30px auto;
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(185, 128, 224, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.8);
            transition: max-height 0.3s ease-out, padding 0.3s ease;
            overflow: hidden;
        }

        .add-task-form.collapsed {
            max-height: 60px;
            padding: 10px 20px;
        }

        .form-content {
            transition: opacity 0.3s ease;
        }

        .add-task-form.collapsed .form-content {
            opacity: 0;
            pointer-events: none;
        }

        .form-title {
            font-size: 1.3rem;
            color: var(--primary);
            margin-bottom: 15px;
            text-align: center;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .form-title::after {
            content: "▼";
            font-size: 0.8rem;
            margin-left: 10px;
            transition: transform 0.3s;
        }

        .add-task-form.collapsed .form-title::after {
            transform: rotate(-90deg);
        }

        .task-board {
            position: relative;
            min-height: calc(100vh - 280px);
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            margin: 0 auto;
            padding: 20px;
            box-shadow: inset 0 0 15px rgba(185, 128, 224, 0.1);
            overflow: hidden;
        }

        .arrange-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }

        .form-select.compact {
            padding: 5px 25px 5px 10px;
            font-size: 0.9rem;
            background-size: 12px;
            background-position: right 8px center;
        }


        .card {
            background-color: var(--card);
            border-radius: 18px;
            box-shadow: 0 8px 20px rgba(185, 128, 224, 0.08);
            padding: 24px;
            transition: transform 0.3s, box-shadow 0.3s;
            position: absolute;
            width: 280px;
            overflow: hidden;
            border: 2px solid transparent;
            cursor: move;
            z-index: 1;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(185, 128, 224, 0.15);
            z-index: 10;
        }

        .high-priority {
            border-color: var(--high-priority);
        }

        .medium-priority {
            border-color: var(--medium-priority);
        }

        .low-priority {
            border-color: var(--low-priority);
        }

        .completed {
            border-color: var(--completed);
        }

        .priority-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            z-index: 2;
        }

        .high-priority .priority-badge {
            background-color: var(--high-priority);
        }

        .medium-priority .priority-badge {
            background-color: var(--medium-priority);
        }

        .low-priority .priority-badge {
            background-color: var(--low-priority);
        }

        .completed .priority-badge {
            background-color: var(--completed);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--text);
            padding-right: 35px;
        }

        .card-desc {
            font-size: 0.95rem;
            color: var(--text-light);
            margin-bottom: 22px;
            line-height: 1.5;
            min-height: 1.5em;
        }

        .progress-container {
            width: 100%;
            background-color: #f8f0fd;
            border-radius: 12px;
            height: 10px;
            margin-top: 15px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .progress-bar {
            height: 100%;
            border-radius: 12px;
            transition: width 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .high-priority .progress-bar {
            background-image: linear-gradient(to right, #f47c7c, #e66767);
        }

        .medium-priority .progress-bar {
            background-image: linear-gradient(to right, #f7a76a, #e99557);
        }

        .low-priority .progress-bar {
            background-image: linear-gradient(to right, #f7d977, #e9c95a);
        }

        .completed .progress-bar {
            background-image: linear-gradient(to right, #92ca92, #74bb74);
        }

        .progress-value {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 0.9rem;
            color: var(--text-light);
        }

        .progress-slider {
            width: 100%;
            margin-top: 15px;
            -webkit-appearance: none;
            height: 6px;
            background: #f0e3f8;
            border-radius: 5px;
            outline: none;
            opacity: 0.8;
            transition: opacity 0.2s;
            cursor: pointer;
        }

        .progress-slider:hover {
            opacity: 1;
        }

        .progress-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary);
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(185, 128, 224, 0.3);
        }

        .progress-slider::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 5px rgba(185, 128, 224, 0.3);
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 5px 0 15px;
            flex-wrap: wrap;
            padding: 8px;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(185, 128, 224, 0.08);
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            border: 1px solid rgba(255, 255, 255, 0.8);
        }

        .legend-item {
            display: flex;
            align-items: center;
            font-size: 0.85rem;
            color: var(--text);
            padding: 5px 10px;
            border-radius: 8px;
            background-color: #fefbff;
            box-shadow: 0 2px 5px rgba(185, 128, 224, 0.05);
        }


        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .card-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 15px;
            gap: 8px;
        }

        .action-button {
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: 5px 10px;
            font-size: 0.9rem;
            border-radius: 5px;
            transition: background-color 0.2s, color 0.2s;
        }

        .action-button:hover {
            background-color: rgba(185, 128, 224, 0.1);
            color: var(--primary);
        }

        .action-button.delete {
            color: #e66767;
        }

        .action-button.delete:hover {
            background-color: rgba(230, 103, 103, 0.1);
            color: #c95151;
        }

        .empty-state {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            padding: 40px 20px;
            color: var(--text-light);
            font-size: 1.1rem;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: var(--primary-light);
        }

        .status-dropdown {
            position: absolute;
            top: 50px;
            right: 15px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 10px 0;
            z-index: 100;
            display: none;
        }

        .status-dropdown.show {
            display: block;
        }

        .status-option {
            padding: 8px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.2s;
        }

        .status-option:hover {
            background-color: #f8f0fd;
        }

        .status-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }

        .status-high {
            background-color: var(--high-priority);
        }

        .status-medium {
            background-color: var(--medium-priority);
        }

        .status-low {
            background-color: var(--low-priority);
        }

        .status-completed {
            background-color: var(--completed);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 10px;
            }

            .form-group {
                min-width: 100%;
            }

            .card {
                width: calc(100% - 40px);
                position: relative;
                margin-bottom: 20px;
            }

            .task-board {
                position: relative;
                min-height: calc(100vh - 220px);
                height: calc(100vh - 220px);
                background-color: rgba(255, 255, 255, 0.3);
                border-radius: 20px;
                margin: 0 auto;
                padding: 20px;
                box-shadow: inset 0 0 15px rgba(185, 128, 224, 0.1);
                overflow: auto;
            }

        }
    </style>
</head>
<body>
<div class="header">
    <h1>✨ 每日任务追踪 ✨</h1>
    <div class="date" id="current-date"></div>
</div>

<div class="legend">
    <div class="legend-item">
        <div class="legend-color" style="background-color: var(--high-priority);">🔴</div>
        <span>紧急任务</span>
    </div>
    <div class="legend-item">
        <div class="legend-color" style="background-color: var(--medium-priority);">🟠</div>
        <span>次要紧急</span>
    </div>
    <div class="legend-item">
        <div class="legend-color" style="background-color: var(--low-priority);">🟡</div>
        <span>一般任务</span>
    </div>
    <div class="legend-item">
        <div class="legend-color" style="background-color: var(--completed);">🟢</div>
        <span>已完成</span>
    </div>
</div>

<div class="add-task-form" id="add-task-form">
    <div class="form-title" id="form-title">添加新任务</div>
    <div class="form-content">
        <div class="form-row">
            <div class="form-group">
                <label class="form-label" for="task-title">任务名称</label>
                <input type="text" id="task-title" class="form-input" placeholder="输入任务名称">
            </div>
            <div class="form-group">
                <label class="form-label" for="task-desc">任务描述 (可选)</label>
                <input type="text" id="task-desc" class="form-input" placeholder="简短描述">
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label class="form-label" for="task-priority">优先级</label>
                <select id="task-priority" class="form-select">
                    <option value="high-priority">紧急 (🔴)</option>
                    <option value="medium-priority">次要紧急 (🟠)</option>
                    <option value="low-priority" selected>一般 (🟡)</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label" for="task-progress">初始进度</label>
                <input type="number" id="task-progress" class="form-input" placeholder="0-100" min="0" max="100"
                       value="0">
            </div>
        </div>
        <button id="add-task-btn" class="form-button">添加任务</button>
    </div>
</div>

<!-- 添加在task-board上方 -->
<div class="arrange-controls">
    <select id="arrange-select" class="form-select compact">
        <option value="">排列方式</option>
        <option value="grid">网格排列</option>
        <option value="circle">圆形排列</option>
        <option value="horizontal">水平排列</option>
        <option value="vertical">垂直排列</option>
        <option value="diagonal">对角线排列</option>
    </select>
</div>

<div class="task-board" id="task-board">
    <!-- Tasks will be added here dynamically -->
</div>

<!-- Status dropdown menu (hidden by default) -->
<div class="status-dropdown" id="status-dropdown">
    <div class="status-option" data-priority="high-priority">
        <div class="status-color status-high"></div>
        <span>紧急任务 (🔴)</span>
    </div>
    <div class="status-option" data-priority="medium-priority">
        <div class="status-color status-medium"></div>
        <span>次要紧急 (🟠)</span>
    </div>
    <div class="status-option" data-priority="low-priority">
        <div class="status-color status-low"></div>
        <span>一般任务 (🟡)</span>
    </div>
    <div class="status-option" data-priority="completed">
        <div class="status-color status-completed"></div>
        <span>已完成 (🟢)</span>
    </div>
</div>

<script>
    // Set current date
    function setCurrentDate() {
        const now = new Date();
        const options = {year: 'numeric', month: 'long', day: 'numeric', weekday: 'long'};
        document.getElementById('current-date').textContent = now.toLocaleDateString('zh-CN', options);
    }

    // Generate unique ID for tasks
    function generateId() {
        return '_' + Math.random().toString(36).substr(2, 9);
    }

    // Sample initial tasks
    const initialTasks = [
        {
            id: generateId(),
            title: "书籍合同处理",
            description: "龙文娟",
            priority: "completed",
            progress: 100,
            symbol: "🟢",
            position: {x: 50, y: 50}
        },
        {
            id: generateId(),
            title: "周日口头汇报祛风理论",
            description: "",
            priority: "medium-priority",
            progress: 65,
            symbol: "🟠",
            position: {x: 350, y: 50}
        },
        {
            id: generateId(),
            title: "研究生劳务报销",
            description: "陈香昱、甘玉婷、雷媛娇毕业小论文",
            priority: "medium-priority",
            progress: 45,
            symbol: "🟠",
            position: {x: 650, y: 50}
        },
        {
            id: generateId(),
            title: "强强院-Meta",
            description: "",
            priority: "low-priority",
            progress: 30,
            symbol: "🟡",
            position: {x: 50, y: 250}
        },
        {
            id: generateId(),
            title: "冯-方案注册，protocol撰写",
            description: "",
            priority: "high-priority",
            progress: 55,
            symbol: "🔴",
            position: {x: 350, y: 250}
        },
        {
            id: generateId(),
            title: "方剂学杂志报销",
            description: "差一张发票",
            priority: "low-priority",
            progress: 80,
            symbol: "🟡",
            position: {x: 650, y: 250}
        },
        {
            id: generateId(),
            title: "简历更新及投递简历",
            description: "",
            priority: "medium-priority",
            progress: 20,
            symbol: "🟠",
            position: {x: 200, y: 450}
        }
    ];

    // Get symbol for priority
    function getSymbolForPriority(priority) {
        switch (priority) {
            case 'high-priority':
                return '🔴';
            case 'medium-priority':
                return '🟠';
            case 'low-priority':
                return '🟡';
            case 'completed':
                return '🟢';
            default:
                return '🟡';
        }
    }

    // Create task card element
    function createTaskCard(task) {
        const cardElement = document.createElement('div');
        cardElement.className = `card ${task.priority}`;
        cardElement.id = task.id;
        cardElement.style.left = `${task.position.x}px`;
        cardElement.style.top = `${task.position.y}px`;

        cardElement.innerHTML = `
                <div class="priority-badge" onclick="toggleStatusDropdown(event, '${task.id}')">${task.symbol}</div>
                <div class="card-title">${task.title}</div>
                <div class="card-desc">${task.description}</div>
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${task.progress}%"></div>
                </div>
                <div class="progress-value">
                    <span>进度</span>
                    <span>${task.progress}%</span>
                </div>
                <input type="range" class="progress-slider" min="0" max="100" value="${task.progress}" oninput="updateProgress(this)">
                <div class="card-actions">
                    <button class="action-button" onclick="editTask('${task.id}')">编辑</button>
                    <button class="action-button delete" onclick="deleteTask('${task.id}')">删除</button>
                </div>
            `;

        // Make card draggable
        makeDraggable(cardElement);

        return cardElement;
    }

    // Make element draggable
    function makeDraggable(element) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

        element.onmousedown = dragMouseDown;

        function dragMouseDown(e) {
            e = e || window.event;
            e.preventDefault();
            // Get the mouse cursor position at startup
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            // Call a function whenever the cursor moves
            document.onmousemove = elementDrag;

            // Bring card to front
            element.style.zIndex = "100";
        }

        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            // Calculate the new cursor position
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            // Set the element's new position
            element.style.top = (element.offsetTop - pos2) + "px";
            element.style.left = (element.offsetLeft - pos1) + "px";
        }

        function closeDragElement() {
            // Stop moving when mouse button is released
            document.onmouseup = null;
            document.onmousemove = null;
            element.style.zIndex = "1";

            // Save position to task
            const taskId = element.id;
            const taskIndex = tasks.findIndex(task => task.id === taskId);
            if (taskIndex !== -1) {
                tasks[taskIndex].position = {
                    x: element.offsetLeft,
                    y: element.offsetTop
                };
                saveTasksToLocalStorage();
            }
        }
    }

    // Render all tasks
    function renderTasks(tasks) {
        const board = document.getElementById('task-board');
        board.innerHTML = '';

        if (tasks.length === 0) {
            const emptyState = document.createElement('div');
            emptyState.className = 'empty-state';
            emptyState.innerHTML = `
                    <div class="empty-icon">📝</div>
                    <p>暂无任务，添加一个吧！</p>
                `;
            board.appendChild(emptyState);
            return;
        }

        tasks.forEach(task => {
            const cardElement = createTaskCard(task);
            board.appendChild(cardElement);
        });
    }

    // Update task progress
    function updateProgress(slider) {
        const value = parseInt(slider.value);
        const card = slider.closest('.card');
        const progressBar = card.querySelector('.progress-bar');
        const progressValue = card.querySelector('.progress-value span:last-child');
        const taskId = card.id;

        progressBar.style.width = value + '%';
        progressValue.textContent = value + '%';

        // Update task in state
        const taskIndex = tasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            tasks[taskIndex].progress = value;

            // Change to completed if 100%
            if (value === 100 && tasks[taskIndex].priority !== 'completed') {
                updateTaskPriority(taskId, 'completed');
            }

            // Store updated tasks
            saveTasksToLocalStorage();
        }
    }

    // Toggle status dropdown
    function toggleStatusDropdown(event, taskId) {
        event.stopPropagation();
        const dropdown = document.getElementById('status-dropdown');

        // Position dropdown near the badge
        const badge = event.target;
        const rect = badge.getBoundingClientRect();
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;
        dropdown.style.left = `${rect.left + window.scrollX - 100}px`;

        // Toggle display
        dropdown.classList.toggle('show');

        // Set current task ID as data attribute
        dropdown.setAttribute('data-task-id', taskId);

        // Add click event listeners to options if not already added
        if (!dropdown.hasAttribute('data-initialized')) {
            const options = dropdown.querySelectorAll('.status-option');
            options.forEach(option => {
                option.addEventListener('click', function () {
                    const priority = this.getAttribute('data-priority');
                    const taskId = dropdown.getAttribute('data-task-id');
                    updateTaskPriority(taskId, priority);
                    dropdown.classList.remove('show');
                });
            });
            dropdown.setAttribute('data-initialized', 'true');
        }
    }

    // Update task priority
    function updateTaskPriority(taskId, priority) {
        const taskIndex = tasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            tasks[taskIndex].priority = priority;
            tasks[taskIndex].symbol = getSymbolForPriority(priority);

            // Update card in DOM
            const card = document.getElementById(taskId);
            card.className = `card ${priority}`;
            card.querySelector('.priority-badge').textContent = tasks[taskIndex].symbol;

            saveTasksToLocalStorage();
        }
    }

    // Edit task
    function editTask(taskId) {
        const task = tasks.find(task => task.id === taskId);
        if (!task) return;

        document.getElementById('task-title').value = task.title;
        document.getElementById('task-desc').value = task.description;
        document.getElementById('task-priority').value = task.priority;
        document.getElementById('task-progress').value = task.progress;

        // Remove task from array
        tasks = tasks.filter(t => t.id !== taskId);

        // Update UI
        renderTasks(tasks);
        saveTasksToLocalStorage();

        // Scroll to form
        document.querySelector('.add-task-form').scrollIntoView({behavior: 'smooth'});
    }

    // Add new task
    function addTask() {
        const titleInput = document.getElementById('task-title');
        const descInput = document.getElementById('task-desc');
        const prioritySelect = document.getElementById('task-priority');
        const progressInput = document.getElementById('task-progress');

        const title = titleInput.value.trim();
        if (!title) {
            alert('请输入任务名称');
            return;
        }

        const description = descInput.value.trim();
        const priority = progressInput.value == 100 ? 'completed' : prioritySelect.value;
        const progress = parseInt(progressInput.value) || 0;

        // Find a good position for the new card
        const board = document.getElementById('task-board');
        const boardRect = board.getBoundingClientRect();
        const lastTask = tasks[tasks.length - 1];

        let newPosition = {x: 50, y: 50};
        if (tasks.length > 0) {
            // Place new card in a grid-like pattern
            const rowSize = Math.floor((boardRect.width - 100) / 300);
            const col = tasks.length % rowSize;
            const row = Math.floor(tasks.length / rowSize);
            newPosition = {
                x: 50 + col * 300,
                y: 50 + row * 200
            };
        }

        // Create new task
        const newTask = {
            id: generateId(),
            title: title,
            description: description,
            priority: progress === 100 ? 'completed' : priority,
            progress: progress,
            symbol: progress === 100 ? '🟢' : getSymbolForPriority(priority),
            position: newPosition
        };

        // Add to tasks array
        tasks.push(newTask);

        // Save and render
        saveTasksToLocalStorage();
        renderTasks(tasks);

        // Clear form
        titleInput.value = '';
        descInput.value = '';
        prioritySelect.value = 'low-priority';
        progressInput.value = '0';
    }

    // Delete task
    function deleteTask(taskId) {
        const confirmDelete = confirm('确定要删除这个任务吗？');
        if (confirmDelete) {
            tasks = tasks.filter(task => task.id !== taskId);
            saveTasksToLocalStorage();
            renderTasks(tasks);
        }
    }

    // Save tasks to localStorage
    function saveTasksToLocalStorage() {
        localStorage.setItem('dailyTasks', JSON.stringify(tasks));
    }

    // Load tasks from localStorage
    function loadTasksFromLocalStorage() {
        const savedTasks = localStorage.getItem('dailyTasks');
        if (savedTasks) {
            const parsedTasks = JSON.parse(savedTasks);
            // Ensure all tasks have position data
            return parsedTasks.map(task => {
                if (!task.position) {
                    task.position = {x: 50, y: 50};
                }
                return task;
            });
        }
        return initialTasks;
    }

    // Close dropdown when clicking elsewhere
    document.addEventListener('click', function (event) {
        const dropdown = document.getElementById('status-dropdown');
        if (dropdown.classList.contains('show')) {
            dropdown.classList.remove('show');
        }
        const formTitle = document.getElementById('form-title');
        const addTaskForm = document.getElementById('add-task-form');

        formTitle.addEventListener('click', function () {
            addTaskForm.classList.toggle('collapsed');

            // If form is being opened, scroll to it
            if (!addTaskForm.classList.contains('collapsed')) {
                addTaskForm.scrollIntoView({behavior: 'smooth'});
            }
        });

// Initially collapse the form to maximize space for cards
        addTaskForm.classList.add('collapsed');
    });

    // Arrange cards in different patterns
    function arrangeCards(pattern) {
        const board = document.getElementById('task-board');
        const boardRect = board.getBoundingClientRect();
        const boardWidth = boardRect.width;
        const boardHeight = Math.max(600, boardRect.height);
        const cardWidth = 280;
        const cardHeight = 220;
        const padding = 20;

        switch (pattern) {
            case 'grid':
                // Arrange in a grid pattern
                const cols = Math.floor((boardWidth - padding) / (cardWidth + padding));
                tasks.forEach((task, index) => {
                    const col = index % cols;
                    const row = Math.floor(index / cols);
                    task.position = {
                        x: padding + col * (cardWidth + padding),
                        y: padding + row * (cardHeight + padding)
                    };
                });
                break;

            case 'circle':
                // Arrange in a circle
                const centerX = boardWidth / 2;
                const centerY = boardHeight / 2;
                const radius = Math.min(centerX, centerY) - cardWidth / 2 - padding;
                tasks.forEach((task, index) => {
                    const angle = (index / tasks.length) * 2 * Math.PI;
                    task.position = {
                        x: centerX + radius * Math.cos(angle) - cardWidth / 2,
                        y: centerY + radius * Math.sin(angle) - cardHeight / 2
                    };
                });
                break;

            case 'horizontal':
                // Arrange in a horizontal line
                tasks.forEach((task, index) => {
                    task.position = {
                        x: padding + index * (cardWidth + padding),
                        y: boardHeight / 2 - cardHeight / 2
                    };
                });
                break;

            case 'vertical':
                // Arrange in a vertical line
                tasks.forEach((task, index) => {
                    task.position = {
                        x: boardWidth / 2 - cardWidth / 2,
                        y: padding + index * (cardHeight + padding)
                    };
                });
                break;

            case 'diagonal':
                // Arrange in a diagonal line
                const maxDiagonal = Math.min(boardWidth - cardWidth - padding, boardHeight - cardHeight - padding);
                const step = maxDiagonal / (tasks.length || 1);
                tasks.forEach((task, index) => {
                    task.position = {
                        x: padding + index * step,
                        y: padding + index * step
                    };
                });
                break;
        }

        saveTasksToLocalStorage();
        renderTasks(tasks);
    }

    // Initialize app
    let tasks = [];

    document.addEventListener('DOMContentLoaded', function () {
        setCurrentDate();

        // Load tasks
        tasks = loadTasksFromLocalStorage();
        renderTasks(tasks);

        // Add task event listener
        document.getElementById('add-task-btn').addEventListener('click', addTask);

        // Add arrangement buttons to the UI


        document.getElementById('arrange-select').addEventListener('change', function () {
            const pattern = this.value;
            if (pattern) {
                arrangeCards(pattern);
            }
        });
    });
</script>
</body>
</html>

