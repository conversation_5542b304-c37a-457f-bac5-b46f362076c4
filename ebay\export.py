# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>reate Time: 2025/3/19 20:04
File Name: export.py
"""
import json
from db.init_db import SessionLocal
from db.db_models import SearchQueries

ebay_path = 'ebay_goods.json'
with open(ebay_path, 'r', encoding='utf-8') as f:
    data = json.load(f)
ebay_data = []
for item in data:
    if len(item) < 20:
        ebay_data.append(item)
ebay_data = list(set(ebay_data))
country_path = 'county_list.json'
with open(country_path, 'r', encoding='utf-8') as f:
    country_data = json.load(f)

county = []
for state in country_data:
    county.append(state['sate_name'])
    for city in state['county_list']:
        county_name = city['county_name'].replace("County", "").replace("city", "")
        county.append(county_name)

county_list = list(set(county))

email_path = 'email_type.json'
with open(email_path, 'r', encoding='utf-8') as f:
    email_data = json.load(f)

email = []
for email_type in email_data:
    email.append(email_type['suffix'])

# 生成base_query的部分（原逻辑不变）
base_query = []
i = 0
exit_flag = False

for ebay_goods in ebay_data:
    for county in county_list:
        for email_type in email:
            base_query.append({
                'goods': ebay_goods,
                'county': county,
                'email_type': email_type
            })
            i += 1
            if i == 100000:
                exit_flag = True
                break
        if exit_flag:
            break
    if exit_flag:
        break

clear_email = ["@gmail.com", "@yahoo.com", "@email", "@outlook.com", "@icloud.com", "@zohomail.com", "@aol.com"]
clear_query = []
for ebay_goods in ebay_data:
    for county in county_list:
        for email_type in clear_email:
            clear_query.append({
                'goods': ebay_goods,
                'county': county,
                'email_type': email_type
            })
print(len(base_query))
print(len(clear_query))
# 高效去重：利用集合的O(installer.txt)查询
base_set = {frozenset(item.items()) for item in base_query}
clear_query = [item for item in clear_query if frozenset(item.items()) not in base_set]

print("clear_query", len(clear_query))


def bulk_insert_queries(queries):
    batch_size = 10
    session = SessionLocal()
    print('总共：', len(queries))
    inserted_count = 0

    try:
        for i in range(0, len(queries), batch_size):
            batch = queries[i:i + batch_size]
            to_insert = []

            for q in batch:
                # 检查记录是否已存在
                existing = session.query(SearchQueries).filter_by(
                    county=q['county'],
                    goods=q['goods'],
                    email_type=q['email_type']
                ).first()

                if not existing:
                    to_insert.append(
                        SearchQueries(
                            county=q['county'],
                            goods=q['goods'],
                            email_type=q['email_type'],
                            crawled_status='pending'
                        )
                    )

            if to_insert:
                session.bulk_save_objects(to_insert)
                session.commit()
                inserted_count += len(to_insert)
                print(f"Inserted {len(to_insert)} queries")

            print(f"当前进度：{i + len(batch)}/{len(queries)} ({(i + len(batch)) / len(queries):.2%})")

    except Exception as e:
        session.rollback()
        print(f"Error during bulk insert: {e}")
    finally:
        session.close()
        print(f"插入完成，共插入 {inserted_count} 条记录")


bulk_insert_queries(clear_query[106000:146000])
