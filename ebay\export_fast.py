# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> we<PERSON>reate Time: 2025/7/10 13:44
File Name:export_fast.py
"""
# -*- coding: utf-8 -*-
"""
Author: jian wei
Create Time: 2025/4/28 15:49
File Name:export_special_emai.py
优化版本
"""
import json
from sqlalchemy import Column, Integer, String, DateTime, text
from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import func
from db.init_db import SessionLocal

Base = declarative_base()


class SearchSpecialQueries(Base):
    __tablename__ = 'search_aol_email'

    id = Column(Integer, primary_key=True, autoincrement=True)
    county = Column(String(1024), nullable=False)
    goods = Column(String(1024), nullable=False)
    email_type = Column(String(255), nullable=False)
    crawled_status = Column(String(255))
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())


export_email = ["@email"]

# 加载ebay商品
ebay_path = 'ebay_goods.json'
with open(ebay_path, 'r', encoding='utf-8') as f:
    data = json.load(f)
ebay_data = []
for item in data:
    if len(item) < 20:
        ebay_data.append(item)
ebay_data = sorted(list(set(ebay_data)))

# 加载地址
country_path = 'county_list.json'
with open(country_path, 'r', encoding='utf-8') as f:
    country_data = json.load(f)
county = []
for state in country_data:
    county.append(state['sate_name'])
    for city in state['county_list']:
        county_name = city['county_name'].replace("County", "").replace("city", "")
        county.append(county_name)

county_list = sorted(list(set(county)))
export_email = sorted(export_email)

# 构造查询
query_list = []
for ebay_goods in ebay_data:
    for county in county_list:
        for email_type in export_email:
            query_list.append({
                'goods': ebay_goods,
                'county': county,
                'email_type': email_type
            })
print('查询数量：', len(query_list))


def bulk_insert_queries_raw_sql(queries):
    """使用原生SQL的最快版本"""
    batch_size = 2000
    session = SessionLocal()
    print('总共：', len(queries))
    inserted_count = 0

    try:
        # 使用原生SQL批量插入，忽略重复项
        print("使用原生SQL批量插入...")

        for i in range(0, len(queries), batch_size):
            batch = queries[i:i + batch_size]

            # 构建批量插入的SQL
            values = []
            for q in batch:
                # 转义单引号防止SQL注入
                county = q['county'].replace("'", "''")
                goods = q['goods'].replace("'", "''")
                email_type = q['email_type'].replace("'", "''")
                values.append(f"('{county}', '{goods}', '{email_type}', 'pending', NOW(), NOW())")

            if values:
                # 使用INSERT IGNORE或ON DUPLICATE KEY UPDATE来避免重复
                insert_sql = f"""
                INSERT IGNORE INTO search_aol_email 
                (county, goods, email_type, crawled_status, created_at, updated_at) 
                VALUES {', '.join(values)}
                """

                result = session.execute(text(insert_sql))
                session.commit()
                inserted_count += result.rowcount
                print(f"插入 {result.rowcount} 条记录")

            print(f"插入进度：{i + len(batch)}/{len(queries)} ({(i + len(batch)) / len(queries):.2%})")

    except Exception as e:
        session.rollback()
        print(f"Error during bulk insert: {e}")
        raise
    finally:
        session.close()
        print(f"插入完成，共插入 {inserted_count} 条记录")


if __name__ == "__main__":
    # 原生SQL版本（推荐用于大数据量，需要MySQL支持INSERT IGNORE）
    bulk_insert_queries_raw_sql(query_list[1036420:1500000])
