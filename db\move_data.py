# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON> Time: 2025/6/18 14:37
File Name:move_data.py
"""
from sqlalchemy import create_engine, MetaData, Table
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.automap import automap_base
import pandas as pd

# 数据库连接字符串
SOURCE_DB_URL = "mysql+pymysql://root:qwer1234@103.225.87.9:13306/ebay"
TARGET_DB_URL = "mysql+pymysql://root:qwer1234@45.251.139.2:13306/ebay"

# 表名
TABLE_NAME = 'search_aol_email'

# 每批读取数量
BATCH_SIZE = 50000

# 创建源和目标数据库引擎
source_engine = create_engine(SOURCE_DB_URL)
target_engine = create_engine(TARGET_DB_URL)

# 自动映射 ORM 类（用于查询数据）
Base = automap_base()
Base.prepare(source_engine, reflect=True)
SearchQueries = Base.classes[TABLE_NAME]

# 创建 Session 用于查询源数据
Session = sessionmaker(bind=source_engine)
session = Session()

# 获取表结构（用于 DataFrame 列匹配）
metadata = MetaData()
source_table = Table(TABLE_NAME, metadata, autoload_with=source_engine)
columns = [c.name for c in source_table.columns]

# 第一次插入时创建表结构（如果目标不存在）
first_batch = True

# 分页查询并写入目标数据库
offset = 0
while True:
    print(f"正在读取偏移 {offset} 的一批数据（{BATCH_SIZE} 条）...")

    results = session.query(SearchQueries).limit(BATCH_SIZE).offset(offset).all()

    if not results:
        break

    # 转换为 DataFrame
    data = [{col: getattr(row, col) for col in columns} for row in results]
    df = pd.DataFrame(data)

    # 写入目标数据库
    if first_batch:
        df.to_sql(TABLE_NAME, target_engine, if_exists='replace', index=False)
        first_batch = False
    else:
        df.to_sql(TABLE_NAME, target_engine, if_exists='append', index=False)

    offset += BATCH_SIZE
    print(f"已写入 {len(df)} 条记录")

print("✅ 数据迁移完成")