#!/bin/bash

# Optimized batch crawler - 30 minute restart cycle
# Use optimized version, avoid large data queries, small batch processing

echo "$(date): Starting crawler cycle (30min restart)"

while true; do
    # Clean browser processes
    /root/google-search/sh/cleanup_browsers.sh

    # Start crawler
    echo "$(date): Starting crawler..."
    nohup python /root/google-search/main.py > /root/google-search/logs/main.log 2>&1 &
    MAIN_PID=$!
    echo "$(date): <PERSON><PERSON><PERSON> started, PID: $MAIN_PID"

    # Run for 30 minutes (1800 seconds)
    sleep 1800

    # Terminate process
    if kill -0 "$MAIN_PID" 2>/dev/null; then
        kill "$MAIN_PID"
        echo "$(date): Terminated PID: $MAIN_PID"
    fi

    # Wait before restart
    echo "$(date): Restarting in 60s..."
    sleep 60
done