# -*- coding: utf-8 -*-
"""
修改要点：
installer.txt. 将截止时间精确到秒级
2. 添加时区处理（可选）
3. 优化时间比较逻辑
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv
import datetime
import time

from db.db_models import SearchResults

def delete_old_search_results():
    # 加载环境变量
    load_dotenv()
    mysql_host = os.getenv('MYSQL_HOST', '************')
    mysql_port = os.getenv('MYSQL_PORT', '13306')
    mysql_user = os.getenv('MYSQL_USER', 'root')
    mysql_password = os.getenv('MYSQL_PASSWORD', 'qwer1234')
    mysql_db = os.getenv('MYSQL_DATABASE', 'ebay')

    DATABASE_URL = f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_db}"
    engine = create_engine(DATABASE_URL,
                          pool_recycle=1800,
                          pool_pre_ping=True,
                          pool_size=5,
                          max_overflow=10)

    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    # 修改点1：设置精确到秒的截止时间
    cutoff_date = datetime.datetime(
        year=2025,
        month=4,
        day=12,
        hour=11,
        minute=2,
        second=0
    )
    # 或者从外部输入获取（更灵活）：
    # cutoff_date_str = "2025-04-08 22:30:00"
    # cutoff_date = datetime.datetime.strptime(cutoff_date_str, "%Y-%m-%d %H:%M:%S")

    print(f"开始删除 {cutoff_date} 之前的数据...")

    # 修改点2：添加时区处理（如果数据库有时间时区）
    # 假设数据库存储的是UTC时间
    # cutoff_date = cutoff_date.astimezone(datetime.timezone.utc)

    # 先获取需要删除的数据总数
    with SessionLocal() as session:
        total_records = session.query(SearchResults).filter(
            SearchResults.date < cutoff_date  # 该字段应为 DATETIME 类型
        ).count()
        print(f"需要删除的记录总数: {total_records}")

    # 设置每批删除的记录数
    batch_size = 10000
    deleted_count = 0
    start_time = time.time()

    try:
        while True:
            with SessionLocal() as session:
                try:
                    # 修改点3：优化查询条件
                    ids_to_delete = session.query(SearchResults.id).filter(
                        SearchResults.date < cutoff_date
                    ).order_by(SearchResults.date.asc()).limit(batch_size).all()

                    if not ids_to_delete:
                        break

                    id_list = [id_tuple[0] for id_tuple in ids_to_delete]

                    # 更精确的删除条件
                    deleted = session.query(SearchResults).filter(
                        SearchResults.id.in_(id_list),
                        SearchResults.date < cutoff_date  # 双重保险
                    ).delete(synchronize_session=False)

                    session.commit()
                    deleted_count += deleted

                    # 打印进度时添加时间戳
                    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    print(f"[{current_time}] 已删除 {deleted_count}/{total_records} 条记录 ({deleted_count / total_records * 100:.2f}%)")

                    time.sleep(0.5)

                except Exception as e:
                    session.rollback()
                    print(f"删除过程中出错: {e}")
                    time.sleep(5)

    except KeyboardInterrupt:
        print("操作被用户中断")

    end_time = time.time()
    duration = end_time - start_time
    print(f"删除操作完成，共删除 {deleted_count} 条记录")
    print(f"总耗时: {duration:.2f} 秒")

    # 可选: 删除完成后优化表
    try:
        with engine.connect() as conn:
            print("正在优化表...")
            conn.execute(text("OPTIMIZE TABLE search_results"))
            print("表优化完成")
    except Exception as e:
        print(f"表优化失败: {e}")


if __name__ == "__main__":
    delete_old_search_results()