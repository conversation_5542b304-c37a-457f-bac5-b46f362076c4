# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> wei
<PERSON>reate Time: 2025/3/31 10:30
File Name:export_.py
"""
# -*- coding: utf-8 -*-
"""
Author: jian wei
Create Time: 2025/3/19 20:04
File Name: export.py
"""
import json
from db.init_db import SessionLocal
from db.db_models import SearchQueries


def load_json_file(file_path):
    """从JSON文件加载数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def get_ebay_goods():
    """获取eBay商品列表"""
    data = load_json_file('ebay_goods.json')
    # 过滤长度小于20的商品并去重
    ebay_data = list(set(item for item in data if len(item) < 20))
    return ebay_data


def get_counties():
    """获取县列表"""
    country_data = load_json_file('county_list.json')
    counties = []

    for state in country_data:
        counties.append(state['sate_name'])
        for city in state['county_list']:
            county_name = city['county_name'].replace("County", "").replace("city", "")
            counties.append(county_name)

    return list(set(counties))


def bulk_insert_queries(queries, start_idx, end_idx):
    """批量插入数据到数据库"""
    batch_size = 1000  # 增加批量大小以提高效率
    session = SessionLocal()

    # 获取要插入的数据片段
    insert_queries = queries[start_idx:end_idx]
    total = len(insert_queries)
    print(f'准备插入数据: {total} 条')

    try:
        for i in range(0, total, batch_size):
            batch = insert_queries[i:i + batch_size]
            session.bulk_save_objects([
                SearchQueries(
                    county=q['county'],
                    goods=q['goods'],
                    email_type=q['email_type'],
                    crawled_status='pending'
                ) for q in batch
            ])

            session.commit()  # 批量提交
            print(f"已插入 {i + len(batch)} / {total} 条数据 ({(i + len(batch)) / total * 100:.2f}%)")
    except Exception as e:
        session.rollback()
        print(f"批量插入过程中出错: {e}")
    finally:
        session.close()


def check_existing_entries(sample_queries):
    """检查样本查询是否已存在于数据库中"""
    session = SessionLocal()
    try:
        existing_count = 0
        for query in sample_queries[:10]:  # 检查前10个作为样本
            exists = session.query(SearchQueries).filter_by(
                county=query['county'],
                goods=query['goods'],
                email_type=query['email_type']
            ).first() is not None
            if exists:
                existing_count += 1

        if existing_count > 5:  # 如果超过一半样本已存在
            print("警告: 大部分查询可能已存在于数据库中。请确认是否继续。")
            return True
        return False
    except Exception as e:
        print(f"检查现有条目时出错: {e}")
        return False
    finally:
        session.close()


def main():
    # 获取所需数据
    ebay_data = get_ebay_goods()
    county_list = get_counties()

    # 定义所需的email类型
    required_emails = ["@gmail.com", "@yahoo.com", "@email", "@outlook.com",
                       "@icloud.com", "@zohomail.com", "@aol.com"]

    # 直接生成所需的查询组合
    all_queries = []
    for goods in ebay_data:
        for county in county_list:
            for email in required_emails:
                all_queries.append({
                    'goods': goods,
                    'county': county,
                    'email_type': email
                })

                # 如果达到我们需要的数量上限，就停止生成
                if len(all_queries) >= 100000:  # 设置上限以避免生成过多数据
                    break
            if len(all_queries) >= 100000:
                break
        if len(all_queries) >= 100000:
            break

    print(f"已生成查询组合: {len(all_queries)} 条")

    # 检查是否有重复数据
    if check_existing_entries(all_queries):
        user_input = input("检测到可能存在重复数据，是否继续? (y/n): ")
        if user_input.lower() != 'y':
            print("操作已取消")
            return

    # 插入10000-20000范围的数据
    start_idx = 10000
    end_idx = 20000

    # 确保索引范围有效
    if start_idx >= len(all_queries):
        print(f"起始索引 {start_idx} 超出了可用数据范围 {len(all_queries)}")
        return

    end_idx = min(end_idx, len(all_queries))
    bulk_insert_queries(all_queries, start_idx, end_idx)
    print(f"已成功插入索引范围 {start_idx}-{end_idx} 的数据")


if __name__ == "__main__":
    main()