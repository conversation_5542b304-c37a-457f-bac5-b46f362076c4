# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> wei
<PERSON>reate Time: 2025/4/12 11:57
File Name:data_cleaner_qt.py
"""
import sys
import time
import logging
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QPushButton, QRadioButton, QButtonGroup, QProgressBar,
                             QMessageBox, QGroupBox, QDateTimeEdit, QSpinBox, QFrame)
from PyQt5.QtCore import Qt, QDateTime, QThread, pyqtSignal
from PyQt5.QtGui import QFont
from sqlalchemy import create_engine, text

# 数据库配置（在此处修改你的数据库信息）
DB_CONFIG = {
    "host": "************",
    "port": "13306",
    "user": "root",
    "password": "qwer1234",
    "database": "ebay"
}


# 删除工作线程
class DeleteWorker(QThread):
    progress = pyqtSignal(int, int, float)  # (当前删除数, 总数, 剩余时间)
    finished = pyqtSignal(bool, int)
    error = pyqtSignal(str)

    def __init__(self, condition, parent=None):
        super().__init__(parent)
        self.condition = condition
        self.total_count = 0
        self.running = True
        self.batch_times = []

    def run(self):
        try:
            # 创建数据库连接
            engine = create_engine(
                f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
                f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}",
                pool_size=5,
                pool_recycle=3600
            )

            # 先查询总数
            with engine.connect() as conn:
                result = conn.execute(text(f"SELECT COUNT(*) FROM search_results {self.condition}"))
                self.total_count = result.scalar()

            if self.total_count == 0:
                self.finished.emit(True, 0)
                return

            deleted = 0
            batch_size = 5000  # 每批删除数量

            while self.running and deleted < self.total_count:
                start_time = time.time()

                with engine.connect() as conn:
                    # 执行删除
                    result = conn.execute(
                        text(f"DELETE FROM search_results {self.condition} LIMIT {batch_size}")
                    )
                    conn.commit()
                    batch_deleted = result.rowcount
                    deleted += batch_deleted

                    # 计算剩余时间
                    batch_duration = time.time() - start_time
                    self.batch_times.append(batch_duration)
                    avg_time = sum(self.batch_times) / len(self.batch_times)
                    remaining = (self.total_count - deleted) / batch_size * avg_time

                    self.progress.emit(deleted, self.total_count, remaining)

                if batch_deleted == 0:
                    break

            self.finished.emit(True, deleted)

        except Exception as e:
            self.error.emit(str(e))
        finally:
            engine.dispose()


# 美化后的主界面
class DatabaseCleanerUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("数据清理专家")
        self.setMinimumSize(680, 400)
        self.setStyleSheet("""
            QMainWindow { background-color: #f5f5f5; }
            QGroupBox { 
                border: 1px solid #ddd; 
                border-radius: 5px;
                margin-top: 1ex;
                font-weight: bold;
            }
            QGroupBox::title { subcontrol-position: top left; padding: 0 3px; }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover { background-color: #45a049; }
            QPushButton:disabled { background-color: #cccccc; }
        """)
        self.init_ui()
        self.setup_logging()

    def init_ui(self):
        main_widget = QWidget()
        layout = QVBoxLayout()

        # 删除条件选择
        condition_group = QGroupBox("选择删除条件")
        condition_layout = QVBoxLayout()

        # ID删除部分
        id_widget = QWidget()
        id_layout = QHBoxLayout()
        self.radio_id = QRadioButton("按ID删除")
        self.spin_id = QSpinBox()
        self.spin_id.setRange(0, 2147483647)
        self.spin_id.setValue(1000)
        self.spin_id.setSingleStep(100)
        id_layout.addWidget(QLabel("删除ID小于:"))
        id_layout.addWidget(self.spin_id)
        id_layout.addStretch()
        id_widget.setLayout(id_layout)

        # 时间删除部分
        time_widget = QWidget()
        time_layout = QHBoxLayout()
        self.radio_time = QRadioButton("按时间删除", checked=True)
        self.time_edit = QDateTimeEdit()
        self.time_edit.setDateTime(QDateTime.currentDateTime())
        self.time_edit.setDisplayFormat("yyyy-MM-dd HH:mm:ss")
        self.time_edit.setCalendarPopup(True)
        time_layout.addWidget(QLabel("删除时间早于:"))
        time_layout.addWidget(self.time_edit)
        time_widget.setLayout(time_layout)

        # 条件分组
        self.cond_group = QButtonGroup()
        self.cond_group.addButton(self.radio_id)
        self.cond_group.addButton(self.radio_time)

        condition_layout.addWidget(self.radio_id)
        condition_layout.addWidget(id_widget)
        condition_layout.addWidget(self.radio_time)
        condition_layout.addWidget(time_widget)
        condition_group.setLayout(condition_layout)

        # 进度显示
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
                height: 24px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                width: 10px;
            }
        """)

        self.lbl_progress = QLabel("就绪")
        self.lbl_progress.setAlignment(Qt.AlignCenter)
        self.lbl_progress.setFont(QFont("Arial", 10, QFont.Bold))

        # 操作按钮
        self.btn_start = QPushButton("开始清理")
        self.btn_stop = QPushButton("停止")
        self.btn_stop.setEnabled(False)

        # 布局组织
        layout.addWidget(condition_group)
        layout.addSpacing(20)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.lbl_progress)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(self.btn_start)
        button_layout.addWidget(self.btn_stop)
        button_layout.addStretch()

        layout.addLayout(button_layout)
        main_widget.setLayout(layout)
        self.setCentralWidget(main_widget)

        # 信号连接
        self.btn_start.clicked.connect(self.start_clean)
        self.btn_stop.clicked.connect(self.stop_clean)

    def setup_logging(self):
        self.logger = logging.getLogger("DBCleaner")
        self.logger.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        fh = logging.FileHandler('cleaner.log')
        fh.setFormatter(formatter)
        self.logger.addHandler(fh)

    def build_condition(self):
        if self.radio_id.isChecked():
            return f"WHERE id < {self.spin_id.value()}"
        else:
            return f"WHERE date < '{self.time_edit.dateTime().toString(Qt.ISODate)}'"

    def start_clean(self):
        try:
            condition = self.build_condition()
            self.worker = DeleteWorker(condition)

            # 连接信号
            self.worker.progress.connect(self.update_progress)
            self.worker.finished.connect(self.on_finished)
            self.worker.error.connect(self.on_error)

            # 更新界面状态
            self.btn_start.setEnabled(False)
            self.btn_stop.setEnabled(True)
            self.lbl_progress.setText("正在初始化...")

            self.worker.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", str(e))

    def stop_clean(self):
        if hasattr(self, 'worker'):
            self.worker.running = False
            self.btn_stop.setEnabled(False)
            self.lbl_progress.setText("操作已停止")

    def update_progress(self, current, total, remaining):
        # 更新进度条
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

        # 更新文字信息
        percent = current / total * 100
        time_str = time.strftime("%H:%M:%S", time.gmtime(remaining))
        self.lbl_progress.setText(
            f"进度: {current}/{total} ({percent:.1f}%)\n"
            f"预估剩余时间: {time_str}"
        )

    def on_finished(self, success, total):
        if success:
            QMessageBox.information(self, "完成", f"成功删除 {total} 条数据")
        self.reset_ui()

    def on_error(self, message):
        QMessageBox.critical(self, "错误", message)
        self.reset_ui()

    def reset_ui(self):
        self.btn_start.setEnabled(True)
        self.btn_stop.setEnabled(False)
        self.lbl_progress.setText("就绪")
        self.progress_bar.reset()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DatabaseCleanerUI()
    window.show()
    sys.exit(app.exec_())
"""
pyinstaller --onefile --windowed data_cleaner_qt.py
"""
