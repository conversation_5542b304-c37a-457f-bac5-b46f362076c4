# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/4/28 15:49
File Name:export_special_emai.py
"""
import json
from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import func
from db.init_db import SessionLocal

Base = declarative_base()


class SearchSpecialQueries(Base):
    __tablename__ = 'search_aol_email'

    id = Column(Integer, primary_key=True, autoincrement=True)
    county = Column(String(1024), nullable=False)
    goods = Column(String(1024), nullable=False)
    email_type = Column(String(255), nullable=False)
    # crawled_status为枚举类型，只能是pending、completed
    crawled_status = Column(String(255))
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())


export_email = ["@email"]

# 加载ebay商品
ebay_path = 'ebay_goods.json'
with open(ebay_path, 'r', encoding='utf-8') as f:
    data = json.load(f)
ebay_data = []
for item in data:
    if len(item) < 20:
        ebay_data.append(item)
# 使用集合去重后排序，确保顺序一致
ebay_data = sorted(list(set(ebay_data)))

# 加载地址
country_path = 'county_list.json'
with open(country_path, 'r', encoding='utf-8') as f:
    country_data = json.load(f)
county = []
for state in country_data:
    county.append(state['sate_name'])
    for city in state['county_list']:
        county_name = city['county_name'].replace("County", "").replace("city", "")
        county.append(county_name)

# 使用集合去重后排序，确保顺序一致
county_list = sorted(list(set(county)))

# 确保email_type也有固定顺序
export_email = sorted(export_email)

# 构造查询
query_list = []
for ebay_goods in ebay_data:
    for county in county_list:
        for email_type in export_email:
            query_list.append({
                'goods': ebay_goods,
                'county': county,
                'email_type': email_type
            })
print('查询数量：', len(query_list))


def bulk_insert_queries(queries):
    batch_size = 10
    session = SessionLocal()
    print('总共：', len(queries))
    inserted_count = 0

    try:
        for i in range(0, len(queries), batch_size):
            batch = queries[i:i + batch_size]
            to_insert = []

            for q in batch:

                # 检查SearchSpecialQueries表中是否已存在记录
                existing_in_special = session.query(SearchSpecialQueries).filter_by(
                    county=q['county'],
                    goods=q['goods'],
                    email_type=q['email_type']
                ).first()

                # 只有在两个表中都不存在的情况下才插入
                if not existing_in_special:
                    to_insert.append(
                        SearchSpecialQueries(
                            county=q['county'],
                            goods=q['goods'],
                            email_type=q['email_type'],
                            crawled_status='pending'
                        )
                    )

            if to_insert:
                session.bulk_save_objects(to_insert)
                session.commit()
                inserted_count += len(to_insert)
            print(f"Inserted {len(to_insert)} queries")

            print(f"当前进度：{i + len(batch)}/{len(queries)} ({(i + len(batch)) / len(queries):.2%})")

    except Exception as e:
        session.rollback()
        print(f"Error during bulk insert: {e}")
    finally:
        session.close()
        print(f"插入完成，共插入 {inserted_count} 条记录")


#  + 221260 + 630 + 1920+20
bulk_insert_queries(query_list[1007800+400:1500000])
