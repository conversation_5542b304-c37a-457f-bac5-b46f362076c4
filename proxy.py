# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/3/19 17:11
File Name:proxy.py
"""
import time
import random
from typing import Optional, List

import requests


class ProxyManager:
    def __init__(self,
                 proxy_url="http://list.rola.info:8088/user_get_ip_list?token=xD3WdIuf5zl2ragb1721654350028&qty={num}&country=&state=&city=&time={minute}&format=txt&protocol=http&filter=installer.txt",
                 num=1, minute=5):
        self.proxy_url = proxy_url.format(num=num, minute=minute)
        self.proxy_list: List[str] = []
        self.last_update = time.time()
        self.update_interval = 120

    def update_proxy_list(self):
        """从api获取新代理并更新列表"""
        response = requests.get(self.proxy_url, timeout=10)
        if response.status_code == 200:
            text = response.text
            # 检查响应内容有效性
            if '未加入白名单' in text or '认证失败' in text:
                print(f"代理API返回错误: {text[:100]}\n")
                return

            # 解析代理列表
            proxies = [line.strip() for line in text.split('\n') if line.strip()]
            self.proxy_list = proxies
            self.last_update = time.time()

    def get_random_proxy(self) -> Optional[str]:
        """获取随机代理"""
        # 检查代理列表是否需要更新
        current_time = time.time()
        need_update = not self.proxy_list or (current_time - self.last_update) > self.update_interval
        if need_update:
            self.update_proxy_list()
        if not self.proxy_list:
            print("警告: 代理列表为空，返回None\n")
            return None
        try:
            proxy = random.choice(self.proxy_list)
            # 移除ip相同的
            # ip = proxy.split(':')[0]
            # for i in self.proxy_list:
            #     if ip in i:
            #         self.proxy_list.remove(i)

            return proxy
        except Exception as e:
            print(f"获取代理时出错: {str(e)}\n")
            return None


class MyProxyManager:
    def __init__(self,
                 proxy_url="https://overseas.proxy.qg.net/get?key=A1LGFVXJ&num={num}&area=990100&isp=&format=txt&seq=\r\n&distinct=false",
                 num=1):
        self.proxy_url = proxy_url.format(num=num)
        self.proxy_list: List[str] = []
        self.last_update = time.time()
        self.update_interval = 120

    def update_proxy_list(self):
        """从api获取新代理并更新列表"""
        response = requests.get(self.proxy_url, timeout=10)
        if response.status_code == 200:
            text = response.text
            # 检查响应内容有效性
            if '未加入白名单' in text or '认证失败' in text:
                print(f"代理API返回错误: {text[:100]}")
                return

            # 解析代理列表
            proxies = [line.strip() for line in text.split('\n') if line.strip()]
            self.proxy_list = proxies
            self.last_update = time.time()

    def get_random_proxy(self) -> Optional[str]:
        """获取随机代理"""
        # 检查代理列表是否需要更新
        current_time = time.time()
        need_update = not self.proxy_list or (current_time - self.last_update) > self.update_interval
        if need_update:
            self.update_proxy_list()
        if not self.proxy_list:
            print("警告: 代理列表为空，返回None\n")
            return None
        try:
            proxy = random.choice(self.proxy_list)
            # 移除ip相同的
            ip = proxy.split(':')[0]
            for i in self.proxy_list:
                if ip in i:
                    self.proxy_list.remove(i)
            return proxy
        except Exception as e:
            print(f"获取代理时出错: {str(e)}\n")
            return None


if __name__ == '__main__':
    proxy_manager = ProxyManager(num=500)
    proxy = proxy_manager.get_random_proxy()
    # 测试访问谷歌
    response = requests.get("https://www.google.com", proxies={"http": f'http://{proxy}'})
    print(response.text)
