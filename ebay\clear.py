# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>reate Time: 2025/3/20 14:11
File Name:clear.py
"""
from db.db_models import SearchQueries
from db.init_db import SessionLocal


def delete_pending_email_queries_orm():
    email_list = ['@yiles.us', '@proton.me', '@mail.ru', '@zohomail.com', '@tutanota.com']

    with SessionLocal() as session:
        result = session.query(SearchQueries).filter(
            SearchQueries.email_type.in_(email_list),
            SearchQueries.crawled_status == 'pending'
        ).delete(synchronize_session=False)

        session.commit()
        return result  # Returns the number of rows deleted


# Example usage
if __name__ == "__main__":
    rows_deleted = delete_pending_email_queries_orm()
    print(f"Deleted {rows_deleted} rows from search_queries table")

